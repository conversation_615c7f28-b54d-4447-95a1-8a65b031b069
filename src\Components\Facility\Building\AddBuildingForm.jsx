import React, { useState, useMemo, useEffect } from "react";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Button from "../../Global/Button";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import { toast } from "react-toastify";
import { createBuilding } from "../../../api/facility"; // API function for creating building
import { useBuildingMasterData } from "../../../hooks/useBuildingMasterData";
import { sanitizeRequest } from "../../../utils/helpers";

const AddBuildingForm = ({facility, onClose, fetchBuildings }) => {
  // Fetch building master data and dropdown options
  const { masterData, statusOptions, typeOptions, occupancyOptions } = useBuildingMasterData();

  // Define field mappings similar to your facility component
  const buildingFields = [
    {
      label: "Building Name *",
      type: "text",
      placeholder: "Building Name",
      name: "name",
    },
    {
      label: "Building Code *",
      type: "text",
      placeholder: "Building Code",
      name: "building_code",
    },
    {
      label: "Status *",
      type: "customDropdown",
      placeholder: "Select Status",
      name: "status",
      options: statusOptions,
    },
    {
      label: "Building Type *",
      type: "customDropdown",
      placeholder: "Select Building Type",
      name: "type",
      options: typeOptions,
    },
    {
      label: "Occupancy Type *",
      type: "customDropdown",
      placeholder: "Select Occupancy Type",
      name: "occupancy_type",
      options: occupancyOptions,
    },
    {
      label: "Building Phone *",
      type: "text",
      placeholder: "Building Phone",
      name: "phone",
    },
    {
      label: "Building Email *",
      type: "email",
      placeholder: "Building Email",
      name: "email",
    },
    {
      label: "Geo Location Code",
      type: "number",
      placeholder: "Geo Location Code",
      name: "geo_location_code",
    },
    {
      label: "Other Code",
      type: "text",
      placeholder: "Other Code",
      name: "other_code",
    },
    {
      label: "Building URL",
      type: "url",
      placeholder: "Building URL",
      name: "building_url",
    },
    {
      label: "Building Notes",
      type: "textarea",
      placeholder: "Building Notes",
      name: "notes",
    },
    {
      label: "Address *",
      type: "text",
      placeholder: "Address",
      name: "address",
    },
  ];

  // Build Yup validation schema using allowed master data values (converted to numbers)
  const buildingSchema = useMemo(
    () =>
      yup.object().shape({
        name: yup.string().required("Building Name is required"),
        building_code: yup.string().required("Building Code is required"),
        status: yup
          .number()
          .oneOf(
            masterData.building_status.map((item) => Number(item.key)),
            "Invalid Status"
          )
          .required("Status is required"),
        type: yup
          .number()
          .oneOf(
            masterData.building_type.map((item) => Number(item.key)),
            "Invalid Building Type"
          )
          .required("Building Type is required"),
        occupancy_type: yup
          .number()
          .oneOf(
            masterData.building_occupancy_type.map((item) => Number(item.key)),
            "Invalid Occupancy Type"
          )
          .required("Occupancy Type is required"),
        phone: yup.string().required("Building Phone is required"),
        email: yup
          .string()
          .email("Enter a valid email")
          .required("Building Email is required"),
        geo_location_code: yup.number().nullable(),
        other_code: yup.string().nullable(),
        building_url: yup.string().url("Enter a valid URL").nullable(),
        notes: yup.string().nullable(),
        address: yup.string().required("Address is required"),
      }),
    [masterData]
  );

  const [loading, setLoading] = useState(false);

  const {
    register,
    handleSubmit,
    control,
    setError,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(buildingSchema),
  });

  // Animation state
  const [show, setShow] = useState(false);

  // Mount/unmount logic for smooth open/close
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Handle form submission using the createBuilding API function
  const submitFormHandler = async (data) => {
    setLoading(true);
    try {
      const sanitizeData = sanitizeRequest(data);
      const response = await createBuilding(facility.facility_id, sanitizeData);
      if (response && response.status === false) {
        throw { response: { data: { data: response.data || {} } } };
      }
      toast.success("Building added successfully!");
      fetchBuildings();
      onClose();
    } catch (error) {
      console.log(error);
      toast.error(error.response && error.response.data ? error.response.data.message : "Error adding building!");
      if (
        error.response &&
        error.response.data &&
        error.response.data.data
      ) {
        const errorsData = error.response.data.data;
        Object.keys(errorsData).forEach((field) => {
          setError(field, { type: "server", message: errorsData[field] });
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end">
      <div
        className={`bg-white w-full h-full max-w-5xl p-4 rounded-lg shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center">
          <h2 className="text-[30px] text-[#4F2683] font-normal">Add Building</h2>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
          >
            &times;
          </button>
        </div>
        <hr className="mb-4 mt-2" />

        <form onSubmit={handleSubmit(submitFormHandler)}>
          <div className="space-y-4">
            <h3 className="text-[20px] text-[#333333] font-medium pb-4">Building Details</h3>
            {buildingFields.map(({ label, type, name, options, placeholder }, idx) => (
              <div key={idx} className="flex mb-2">
                <label className="mr-2 w-1/3 text-[16px] font-normal text-[#333333]">{label}</label>
                <div className="w-2/3">
                  {type === "customDropdown" ? (
                    <Controller
                      control={control}
                      name={name}
                      defaultValue={null}
                      render={({ field }) => (
                        <CustomDropdown
                          value={field.value}
                          options={options}
                          placeholder={placeholder}
                          onSelect={(option) => {
                            const numericValue =
                              typeof option === "object" ? Number(option.value) : Number(option);
                            field.onChange(numericValue);
                          }}
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          className="p-2 border h-11 rounded focus:outline-none focus:ring-1"
                          rounded="rounded"
                          error={errors[name]}
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={type}
                      name={name}
                      placeholder={placeholder}
                      error={errors[name]}
                      {...register(name)}
                    />
                  )}
                  {errors[name] && (
                    <p className="text-red-500 text-sm mt-1">{errors[name].message}</p>
                  )}
                </div>
              </div>
            ))}

            <div className="flex justify-center gap-4 mt-6">
              <Button type="cancel" label="Cancel" onClick={onClose} />
              <Button type="primary" label={loading ? "Saving..." : "Add"} disabled={loading} />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddBuildingForm;

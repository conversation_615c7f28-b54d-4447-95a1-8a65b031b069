import React, { useState, useEffect } from 'react';
import EditableSection from '../Global/EditableSection';
import {
  getPatientInformation,
  updatePatientDetails,
  updateAdmissionDetails,
  updateFacilityDetails,
  updatePatientAddress,
} from '../../api/PatientHub';
import { getFacilities } from '../../api/global';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useCountryMasterData } from '../../hooks/useCountryMasterData';
import { useStateMasterData } from '../../hooks/useStateMasterData';
import formatDateTime from '../../utils/formatDate';
// Add hooks for building/floor/room
import { useBuildingData } from '../../hooks/useBuildingData';
import { useFloorData } from '../../hooks/useFloorData';
import { useRoomData } from '../../hooks/useRoomData';

const Demographic = ({ patientId }) => {
  // Biographic state
  const [biographicData, setBiographicData] = useState({
    FirstName: '',
    MiddleName: '',
    LastName: '',
    Alias: '',
    PreferredName: '',
    Email: '',
    Phone: '',
    status: '',
    DateOfBirth: '',
  });

  // Country / State master data
  const { countries: countryData = [] } = useCountryMasterData();
  const [selectedCountry, setSelectedCountry] = useState(null);
  const states = useStateMasterData(selectedCountry);

  const countryOptions = countryData.map(c => ({ label: c.name, value: c.country_id }));
  const stateOptions = Array.isArray(states)
    ? states.map(s => ({ label: s.name, value: s.state_id }))
    : [];

  // Admission state
  const [admissionData, setAdmissionData] = useState({
    PatientType: '',
    AdmissionDate: '',
    DischargeDate: '-',
    PatientDeceased: '-',
    DateTimeofDeceased: '',
    Confidential: '-',
  });

  // Facility state + master-data
  const [facilityData, setFacilityData] = useState({
    Facility: '',
    Building: '',
    Floor: '',
    Room: '',
    BedNumber: '',
  });
  const [facilityOptions, setFacilityOptions] = useState([]);
  const [selectedFacilityId, setSelectedFacilityId] = useState('');

  // Add building/floor/room selection state
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');

  // Fetch building/floor/room options using hooks
  const rawBuildingOptions = useBuildingData(selectedFacilityId);
  const rawFloorOptions = useFloorData(selectedBuilding);
  const rawRoomOptions = useRoomData(selectedFloor);

  // Map to dropdown format
  const buildingOptions = Array.isArray(rawBuildingOptions)
    ? rawBuildingOptions.map(b => ({
      label: b.name || b.label || '',
      value: b.building_id || b.value || b.id || ''
    }))
    : [];

  const floorOptions = Array.isArray(rawFloorOptions)
    ? rawFloorOptions.map(f => ({
      label: f.name || f.label || '',
      value: f.floor_id || f.value || f.id || ''
    }))
    : [];

  const roomOptions = Array.isArray(rawRoomOptions)
    ? rawRoomOptions.map(r => ({
      label: r.name || r.label || '',
      value: r.room_id || r.value || r.id || ''
    }))
    : [];

  // Get selected facility from localStorage (set by header)
  const selectedFacilityFromHeader = localStorage.getItem("selectedFacility");
  // Helper to get facility name from id
  const getFacilityNameById = (id) => {
    const found = facilityOptions.find(f => f.value === id);
    return found ? found.label : '';
  };

  // Address state
  const [addressData, setAddressData] = useState({
    Address1: '',
    Address2: '',
    country_id: '',
    state_id: '',
    City: '',
    Zip: '',
  });

  // Fetch patient & seed existing values
  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        if (!patientId) return;
        const response = await getPatientInformation({ patient_id: patientId });

        const facilityId = selectedFacilityFromHeader || response.facility || '';
        setSelectedFacilityId(facilityId);

        setTimeout(() => {
          setSelectedBuilding(response.building_id || '');
          setSelectedFloor(response.floor_id || '');
        }, 0);
        // Biographic
        setBiographicData({
          FirstName: response.first_name || '',
          MiddleName: response.middle_name || '',
          LastName: response.last_name || '',
          PreferredName: response.preferred_name || '',
          Alias: response.alias || '',
          Email: response.email || '',
          Phone: response.phone || '',
          status: response.status || '',
          DateOfBirth: response.birth_date ? formatDateTime(response.birth_date) : '',
        });

        // Admission
        setAdmissionData({
          PatientType: response.type || '',
          AdmissionDate: response.arrival_time ? formatDateTime(response.arrival_time) : '',
          DischargeDate: response.discharge_time || '-',
          PatientDeceased: response.death_date ? formatDateTime(response.death_date) : '-',
          DateTimeofDeceased: response.datetime_of_deceased || '',
          Confidential: response.confidentiality_code || '-',
        });

        // Facility
        setFacilityData({
          Facility: getFacilityNameById(selectedFacilityFromHeader || response.facility || ''),
          Building: response.building || '',
          Floor: response.floor || '',
          Room: response.room || '',
          BedNumber: response.beds || '',
        });
        setSelectedFacilityId(selectedFacilityFromHeader || response.facility || '');

        // Address
        setAddressData({
          Address1: response.address_line_1 || '',
          Address2: response.address_line_2 || '',
          country_id: '',
          state_id: '',
          City: response.city || '',
          Zip: response.postal_code || '',
        });
      } catch (error) {
        console.error('API Error:', error);
        toast.error('Failed to fetch patient data.', {
          position: 'top-right', autoClose: 3000, hideProgressBar: true,
          closeOnClick: true, pauseOnHover: true, draggable: true, theme: 'light',
        });
      }
    };
    fetchPatientData();
    // Add selectedFacilityFromHeader as dependency so it updates if header changes
  }, [patientId, selectedFacilityFromHeader, facilityOptions]);

  // Fetch facility master-data
  useEffect(() => {
    const loadFacilities = async () => {
      try {
        const res = await getFacilities();
        const list = (res.data?.data || []).map(f => ({ label: f.name, value: f.facility_id }));
        setFacilityOptions(list);
      } catch (err) {
        console.error('Failed loading facilities:', err);
      }
    };
    loadFacilities();
  }, []);

  // Update facilityData when facility/building/floor changes
  useEffect(() => {
    setFacilityData(prev => ({
      ...prev,
      Building: '',
      Floor: '',
      Room: '',
    }));
    setSelectedBuilding('');
    setSelectedFloor('');
  }, [selectedFacilityId]);

  useEffect(() => {
    setFacilityData(prev => ({
      ...prev,
      Floor: '',
      Room: '',
    }));
    setSelectedFloor('');
  }, [selectedBuilding]);

  useEffect(() => {
    setFacilityData(prev => ({
      ...prev,
      Room: '',
    }));
  }, [selectedFloor]);

  // Generic change handler
  const handleChange = (section, key, value) => {
    switch (section) {
      case 'biographicData': setBiographicData(prev => ({ ...prev, [key]: value })); break;
      case 'admissionData': setAdmissionData(prev => ({ ...prev, [key]: value })); break;
      case 'facilityData': setFacilityData(prev => ({ ...prev, [key]: value })); break;
      case 'addressData': setAddressData(prev => ({ ...prev, [key]: value })); break;
      default: break;
    }
  };

  // Payload mapper
  const mapPayload = (section, data) => {
    switch (section) {
      case 'biographicData':
        return {
          first_name: data.FirstName, middle_name: data.MiddleName, last_name: data.LastName,
          preferred_name: data.PreferredName, email: data.Email, phone: data.Phone,
          birth_date: data.RawBirthDate
        };
      case 'admissionData': {
        const parseDate = str => str && str !== '-' ? new Date(str).toISOString() : null;
        return {
          type: parseInt(data.PatientType, 10) || 0,
          discharge_time: parseDate(data.DischargeDate),
          death_date: data.PatientDeceased !== '-' ? parseDate(data.PatientDeceased) : null,
          confidentiality_code: parseInt(data.Confidential, 10) || 0
        };
      }
      case 'facilityData': {
        // Convert facility name back to id for API
        const facilityObj = facilityOptions.find(f => f.label === data.Facility);
        return { facility_id: facilityObj ? facilityObj.value : '', beds: parseInt(data.BedNumber, 10) || 0 };
      }
      case 'addressData':
        return {
          address_line_1: data.Address1, address_line_2: data.Address2,
          country: data.country_id, state: data.state_id,
          city: data.City, postal_code: data.Zip
        };
      default:
        return data;
    }
  };

  // Save handler
  const handleSave = async (section, data) => {
    try {
      const payload = mapPayload(section, data);
      let resp;
      switch (section) {
        case 'biographicData': resp = await updatePatientDetails(patientId, payload); break;
        case 'admissionData': resp = await updateAdmissionDetails(patientId, payload); break;
        case 'facilityData': resp = await updateFacilityDetails(patientId, payload); break;
        case 'addressData': resp = await updatePatientAddress(patientId, payload); break;
        default: break;
      }
      console.log('API Response:', resp);
      toast.success('Data updated successfully!', { position: 'top-right', autoClose: 3000, hideProgressBar: true, closeOnClick: true, pauseOnHover: true, draggable: true, theme: 'light' });
    } catch (error) {
      console.error('Update error:', error);
      const msg = error?.response?.data?.message || 'Failed to update data. Please try again.';
      toast.error(msg, { position: 'top-right', autoClose: 3000, hideProgressBar: true, closeOnClick: true, pauseOnHover: true, draggable: true, theme: 'light' });
    }
  };

  return (
    <div className="bg-gray-100 min-h-screen">
      {/* Patient Info */}
      <EditableSection
        title="Patient Info"
        data={biographicData}
        onChange={(k, v) => handleChange('biographicData', k, v)}
        dateKeys={['DateOfBirth']}
        onSave={d => handleSave('biographicData', d)}
      />

      {/* Admission Info */}
      <EditableSection
        title="Admission Info"
        data={admissionData}
        onChange={(k, v) => handleChange('admissionData', k, v)}
        onSave={d => handleSave('admissionData', d)}
      />

      {/* Facility */}
      <EditableSection
        title="Facility"
        data={facilityData}
       onChange={(key, value) => {
  if (key === 'Facility') {
    setSelectedFacilityId(value.value);  // for hook
    handleChange('facilityData', key, value.label);  // for UI
  } else if (key === 'Building') {
    setSelectedBuilding(value.value);
    handleChange('facilityData', key, value.label);
  } else if (key === 'Floor') {
    setSelectedFloor(value.value);
    handleChange('facilityData', key, value.label);
  } else if (key === 'Room') {
    handleChange('facilityData', key, value.label);
  } else {
    handleChange('facilityData', key, value);
  }
}}


        onSave={d => handleSave('facilityData', d)}
        dropdownKeys={['Facility', 'Building', 'Floor', 'Room']}
        dropdownOptions={{
          Facility: facilityOptions,
          Building: buildingOptions,
          Floor: floorOptions,
          Room: roomOptions,
        }}
        searchableKeys={['Facility', 'Building', 'Floor', 'Room']}
        editableKeys={['Building', 'Floor', 'Room', 'BedNumber']}
      />

      {/* Address */}
      <EditableSection
        title="Address"
        data={addressData}
        onChange={(key, val) => {
          handleChange('addressData', key, val);
          if (key === 'country_id') {
            setSelectedCountry(val);
            handleChange('addressData', 'state_id', '');
          }
        }}
        onSave={d => handleSave('addressData', d)}
        dropdownKeys={['country_id', 'state_id']}
        dropdownOptions={{ country_id: countryOptions, state_id: stateOptions }}
        searchableKeys={['country_id']}
      />

      <ToastContainer />
    </div>
  );
};

export default Demographic;

import React, { useState, useEffect } from "react";
import Button from "../Global/Button";

const AddDeviceModal = ({ open, onClose, onSave, newEntry, setNewEntry }) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (open) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [open]);

  if (!open) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleFormSubmit = (e) => {
    e.preventDefault();
    onSave();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Device</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device"
                value={newEntry.device}
                onChange={e => setNewEntry({ ...newEntry, device: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Group*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device Group"
                value={newEntry.deviceGroup}
                onChange={e => setNewEntry({ ...newEntry, deviceGroup: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Building
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Building"
                value={newEntry.building}
                onChange={e => setNewEntry({ ...newEntry, building: e.target.value })}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Floor
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Floor"
                value={newEntry.floor}
                onChange={e => setNewEntry({ ...newEntry, floor: e.target.value })}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Room
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Room"
                value={newEntry.room}
                onChange={e => setNewEntry({ ...newEntry, room: e.target.value })}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={handleClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDeviceModal;

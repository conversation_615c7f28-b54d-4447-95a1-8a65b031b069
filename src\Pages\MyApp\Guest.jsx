import React, { useState, useMemo, useEffect, useCallback } from "react";
import moment from "moment";
import { useTranslation } from "react-i18next";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import AddGuestForm from "../../Components/Guest/AddGuestForm";
import EditGuestForm from "../../Components/Guest/EditGuestForm";
import ViewGuestDetails from "../../Components/Guest/ViewGuestDetails";
import ViewGuestHistory from "../../Components/Guest/ViewGuestHistory";
import viewicon from "../../Images/ViewIcon.svg";
import editicon from "../../Images/editPen.svg";
import history from "../../Images/ChechInHistory.svg";
import Delete from "../../Images/Delete.svg";
import { getGuests, deleteGuest } from "../../api/guest";
import { toast } from "react-toastify";
import Swal from "sweetalert2";
import Loader from "../../Components/Loader";

const Guest = () => {
  const { t } = useTranslation();

  // State management
  const [guests, setGuests] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  // Search and sorting state
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState("ASC");

  // State for filter tab (All Guests / Today Guest)
  const [activeTab, setActiveTab] = useState("all");

  // Filter options for the tabs
  const filterOptions = [
    { value: "all", label: t('guest.all_guests') },
    { value: "today", label: t('guest.today_guest') },
  ];

  // Mock data for testing
 
  // Fetch guests from API
  const fetchGuests = useCallback(async () => {
    setLoading(true);
    try {
      // Try API first, fallback to mock data if API fails
      try {
        const params = {
          search: searchQuery || undefined,
          sortBy,
          sortOrder,
        };

        const response = await getGuests(params);
        console.log("API Response:", response);

        // Handle the API response format: {status: true, data: {data: [...], totalItems: ...}, message: "..."}
        const guestData = response.data?.data || response.data || response || [];



        // Format the data to match the expected structure
        const formattedGuests = Array.isArray(guestData) ? guestData.map((guest) => {
          return {
            id: guest.id,
            firstName: guest.first_name,
            lastName: guest.last_name,
            name: `${guest.first_name} ${guest.last_name}`,
            email: guest.email,
            mobilePhone: guest.mobile_phone || guest.phone,
            company: guest.company || "N/A",
            lastVisited: guest.last_visited || "N/A",
            nextVisit: guest.next_visit || "N/A",
            isPrivate: guest.private_visitor || guest.is_private ? "Yes" : "No",
            totalVisits: guest.total_visits || 0,
          };
        }) : [];

        setGuests(formattedGuests);

        // If API returns empty data, show demo data for testing
        if (formattedGuests.length === 0) {
          console.log("API returned empty data, showing demo data for testing");
          // let filteredMockGuests = mockGuests;
          // if (searchQuery) {
          //   filteredMockGuests = mockGuests.filter(guest =>
          //     guest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          //     guest.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
          //     guest.mobilePhone.includes(searchQuery)
          //   );
          // }
          // setGuests(filteredMockGuests);
        }
      } catch (apiError) {
        console.warn("API failed, using mock data:", apiError);

        // Apply search filter to mock data
        // let filteredMockGuests = mockGuests;
        // if (searchQuery) {
        //   filteredMockGuests = mockGuests.filter(guest =>
        //     guest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        //     guest.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        //     guest.mobilePhone.includes(searchQuery)
        //   );
        // }

        // setGuests(filteredMockGuests);
        toast.info("Using demo data - API connection failed");
      }
    } catch (error) {
      console.error("Error fetching guests:", error);
      toast.error("Failed to fetch guests. Please try again.");
      setGuests([]);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, sortBy, sortOrder]);

  // Fetch guests on component mount and when dependencies change
  useEffect(() => {
    fetchGuests();
  }, [fetchGuests]);

  // Search and sort handlers
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  const handleSortClick = (column, sortDirection) => {
    console.log('Guest sorting:', { column, sortDirection });

    // Extract column identifier from column object
    const columnId = column.id || column.selector || column.name;
    console.log('Sorting by column:', columnId, 'Direction:', sortDirection);

    setSortBy(columnId);
    setSortOrder(sortDirection.toUpperCase());
  };

  // Filter and sort guests based on active tab and sorting preferences
  const filteredGuests = useMemo(() => {
    // First filter by active tab
    let filtered = guests.filter((guest) => {
      if (activeTab === "all") return true;
      if (activeTab === "today") {
        const guestLastVisited = moment(guest.lastVisited, "DD-MMM-YYYY | hh:mm A");
        return guestLastVisited.isSame(moment(), "day");
      }
      return true;
    });

    // Then apply sorting if sortBy is specified
    if (sortBy && filtered.length > 0) {
      filtered = [...filtered].sort((a, b) => {
        let aValue = a[sortBy];
        let bValue = b[sortBy];

        // Handle different data types
        if (typeof aValue === 'string') {
          aValue = aValue.toLowerCase();
        }
        if (typeof bValue === 'string') {
          bValue = bValue.toLowerCase();
        }

        // Handle numbers
        if (typeof aValue === 'number' && typeof bValue === 'number') {
          return sortOrder === 'ASC' ? aValue - bValue : bValue - aValue;
        }

        // Handle strings and other types
        if (aValue < bValue) {
          return sortOrder === 'ASC' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortOrder === 'ASC' ? 1 : -1;
        }
        return 0;
      });
    }

    return filtered;
  }, [guests, activeTab, sortBy, sortOrder]);

  // Guest operation handlers
  const handleAddGuest = () => {
    setShowAddModal(true);
  };

  const handleAddGuestSuccess = () => {
    setShowAddModal(false);
    fetchGuests(); // Refresh the data
  };

  const handleEditGuestSuccess = () => {
    setShowEditModal(false);
    fetchGuests(); // Refresh the data
  };

  const handleDeleteGuest = async (guestId) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      confirmButtonColor: "#d33",
      cancelButtonColor: "#3085d6",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        try {
          await deleteGuest(guestId);
          toast.success("Guest deleted successfully!");
        } catch (apiError) {
          console.warn("API failed:", apiError);
          if (apiError.response?.status === 401) {
            toast.error("Please login to delete guests.");
          } else if (apiError.response?.status === 404) {
            toast.error("Guest not found.");
          } else {
            toast.success("Guest deleted successfully! (Demo mode)");
          }
        }
        fetchGuests(); // Refresh the data
      } catch (error) {
        console.error("Error deleting guest:", error);
        toast.error("Failed to delete guest. Please try again.");
      }
    }
  };

  const columns = [
    { id: 'name', name: t('guest.guest_name'), selector: (row) => row.name, sortable: true },
    { id: 'email', name: t('guest.email_id'), selector: (row) => row.email, sortable: true },
    { id: 'company', name: t('guest.company'), selector: (row) => row.company, sortable: true },
    { id: 'lastVisited', name: t('guest.last_visited'), selector: (row) => row.lastVisited, sortable: true },
    { id: 'nextVisit', name: t('guest.next_visit'), selector: (row) => row.nextVisit, sortable: true },
    { id: 'isPrivate', name: t('guest.is_private'), selector: (row) => row.isPrivate, sortable: true },
    { id: 'totalVisits', name: t('guest.total_visits'), selector: (row) => row.totalVisits, sortable: true },
    {
      name: t('guest.actions'),
      cell: (row) => (
        <div className="flex space-x-2">
           <div className="relative group">
           <img
            src={viewicon}
            alt={t('guest.view')}
            className="bg-[#EEE9F2] px-1 py-1 rounded cursor-pointer"
            onClick={() => {
              setSelectedGuest(row);
              setShowViewModal(true);
            }}
          />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('guest.view_guest')}
            </div>
          </div>
          <div className="relative group">
          <img
            src={editicon}
            alt={t('guest.edit')}
            className="bg-[#EEE9F2] px-1 py-1 rounded cursor-pointer"
            onClick={() => {
              setSelectedGuest(row);
              setShowEditModal(true);
            }}
          />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              {t('guest.edit_guest')}
            </div>
          </div>
          <div className="relative group">
          <img
            src={history}
            alt={t('guest.history')}
            className="bg-[#EEE9F2] px-1 py-1 rounded cursor-pointer"
            onClick={() => {
              setSelectedGuest(row);
              setShowHistoryModal(true);
            }}
          />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
            {t('guest.view_history')}
            </div>
          </div>
          <div className="relative group">
          <img
            src={Delete}
            alt="Delete"
            className="bg-[#EEE9F2] px-1 py-1 rounded cursor-pointer"
            onClick={() => handleDeleteGuest(row.id)}
          />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              Delete Guest
            </div>
          </div>
        </div>
      ),
    },
  ];

  return (
    <div className="bg-white shadow-md rounded-lg pt-20 ps-20">
      {/* Filter Tabs */}
      <div className="mb-4">
        <FilterButtons
          filter={activeTab}
          onFilterChange={setActiveTab}
          filterOptions={filterOptions}
        />
      </div>



      {/* Render Modals */}
      {showAddModal && (
        <AddGuestForm
          onClose={() => setShowAddModal(false)}
          onSuccess={handleAddGuestSuccess}
        />
      )}
      {showEditModal && selectedGuest && (
        <EditGuestForm
          guest={selectedGuest}
          onClose={() => setShowEditModal(false)}
          onSuccess={handleEditGuestSuccess}
        />
      )}
      {showViewModal && selectedGuest && (
        <ViewGuestDetails guest={selectedGuest} onClose={() => setShowViewModal(false)} />
      )}
      {showHistoryModal && selectedGuest && (
        <ViewGuestHistory guest={selectedGuest} onClose={() => setShowHistoryModal(false)} />
      )}

      {/* Table */}
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title={t('guest.my_guests')}
          searchTerm={searchQuery}
          onSearchChange={handleSearch}
          onSort={handleSortClick}
          columns={columns}
          data={filteredGuests}
          showSearch={true}
          showAddButton={true}
          onAdd={handleAddGuest}
        />
      )}
    </div>
  );
};

export default Guest;

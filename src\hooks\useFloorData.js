import { useState, useCallback, useEffect } from "react";
import { getFloorsByBuilding } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for floor data keyed by buildingId
let floorMasterDataCache = {};

export const useFloorData = (buildingId) => {
  const [floors, setFloors] = useState(
    buildingId && floorMasterDataCache[buildingId]
      ? floorMasterDataCache[buildingId]
      : []
  );

  const fetchFloors = useCallback(async () => {
    if (!buildingId) return;
    try {
      const response = await getFloorsByBuilding(buildingId);
      const floorArray = response.data?.data || [];
      const fetchedFloors = floorArray.map((f) => ({
        label: f.floor_number,
        value: f.floor_id,
      }));
      // Optionally update cache:
      floorMasterDataCache[buildingId] = fetchedFloors;
      setFloors(fetchedFloors);
    } catch (error) {
      console.error("Error fetching floors:", error);
      toast.error("Error fetching floors");
    }
  }, [buildingId]);  

  useEffect(() => {
    fetchFloors();
  }, [buildingId, fetchFloors]);

  return floors;
};

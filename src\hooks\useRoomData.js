import { useState, useCallback, useEffect } from "react";
import { getRoomsByFloor } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for room data keyed by floorId
let roomMasterDataCache = {};

export const useRoomData = (floorId) => {
  const [rooms, setRooms] = useState(
    floorId && roomMasterDataCache[floorId]
      ? roomMasterDataCache[floorId]
      : []
  );

  const fetchRooms = useCallback(async () => {
    if (!floorId) return;
    try {
      const response = await getRoomsByFloor(floorId);
      const roomArray = response.data?.data || [];
      const fetchedRooms = roomArray.map((r) => ({
        label: r.room_number,
        value: r.room_id,
      }));
      roomMasterDataCache[floorId] = fetchedRooms;
      setRooms(fetchedRooms);
    } catch (error) {
      console.error("Error fetching rooms:", error);
      toast.error("Error fetching rooms");
    }
  }, [floorId]);

  useEffect(() => {
    fetchRooms();
  }, [floorId, fetchRooms]);

  return rooms;
};

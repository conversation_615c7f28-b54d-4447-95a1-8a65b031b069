import React, { useEffect, useState } from "react";
import Input from "./Input/Input";
import Button from "./Button";
import CustomDropdown from "./CustomDropdown";
import SearchableStringDropdown from "./SearchableDropdown";
import { FaPencil } from "react-icons/fa6";

const EditableSection = ({
  title,
  data,
  onChange,
  onSave, // Optional prop for external save functionality
  dropdownKeys = [],
  dropdownOptions = {},
  searchableKeys = [], // New prop for searchable fields
  editableKeys, // New prop to control which fields are editable
  editingButton = true, // New prop to control edit button visibility
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [localData, setLocalData] = useState(data);

useEffect(() => {
  setLocalData(data);
}, [data]);

  // // Helper function to format key labels (if needed)
  // const formatKey = (key) => {
  //   const withSpaces = key.replace(/([A-Z])/g, " $1");
  //   return withSpaces.charAt(0).toUpperCase() + withSpaces.slice(1).trim();
  // };

  const handleInputChange = (key, value) => {
    setLocalData((prev) => ({
      ...prev,
      [key]: { ...prev[key], value },
    }));
  };

  const handleSave = () => {
    Object.entries(localData).forEach(([key, { value }]) =>
      onChange(key, value)
    );
    if (onSave) onSave(localData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setLocalData(data);
    setIsEditing(false);
  };

  // Determines if a field is editable
  const isFieldEditable = (key) => {
    if (editableKeys && Array.isArray(editableKeys)) {
      return editableKeys.includes(key);
    }
    return true;
  };

  return (
    <div>
      <div className="bg-white px-4 pb-2 p-2 mb-4 shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-lg my-1">
        <div className="flex justify-between items-center">
          <h3 className="font-normal text-[20px] text-[#4F2683]">{title}</h3>
          {editingButton && !isEditing && (
            <button
              onClick={() => setIsEditing(true)}
              className="rounded-full bg-[#4F2683] hover:bg-[#701ED8] p-1 mt-2"
            >
              <FaPencil className="text-white p-1" size={24} />
            </button>
          )}
        </div>
        <hr className="my-2" />
        <div>
          {isEditing ? (
            <>
              {Object.entries(localData).map(([key, { label, value }]) => (
                <div className="flex items-start mb-2" key={key}>
                  {/* Label Section */}
                  <div className="w-1/4 p-2">
                    <label
                      className="block text-[#7C7C7C] text-[18px] font-normal"
                      htmlFor={key}
                    >
                      {label}
                    </label>
                  </div>
                  {/* Input/Display Section */}
                  <div className="w-3/4">
                    {isFieldEditable(key) ? (
                      dropdownKeys.includes(key) ? (
                        searchableKeys.includes(key) ? (
                          <SearchableStringDropdown
                            options={(dropdownOptions[key] || []).map(
                              (opt) => opt.value
                            )}
                            value={value || ""}
                            onSelect={(option) => handleInputChange(key, option)}
                            placeholder="Select an option"
                            bgColor="bg-white"
                            textColor="text-black"
                            hoverBgColor="hover:bg-[#4F2683]"
                            borderColor="border-gray-300"
                            className="w-full"
                            rounded="rounded-md"
                          />
                        ) : (
                          <CustomDropdown
                            options={dropdownOptions[key] || []}
                            value={value || ""}
                            onSelect={(option) => handleInputChange(key, option)}
                            placeholder="Select an option"
                            searchable={false}
                            bgColor="bg-white"
                            textColor="text-black"
                            hoverBgColor="hover:bg-[#4F2683]"
                            borderColor="border-gray-300"
                            className="w-full h-10"
                            rounded="rounded-md"
                          />
                        )
                      ) : (
                        <Input
                          type="text"
                          className="text-[16px] font-normal"
                          id={key}
                          label=""
                          value={value || ""}
                          onChange={(e) =>
                            handleInputChange(key, e.target.value)
                          }
                        />
                      )
                    ) : (
                      <p className="text-[16px] font-normal text-[#7C7C7C]">
                        {value}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </>
          ) : (
            <div>
              {Object.entries(localData).map(([key, { label, value }]) => (
                <div className="flex items-start mb-4" key={key}>
                  {/* Label Section */}
                  <div className="w-1/4">
                    <p className="text-[#7C7C7C] font-normal">{label}</p>
                  </div>
                  {/* Value Section */}
                  <div className="w-3/4">
                    <p className="text-[#7C7C7C] font-[600]">{value}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
      {isEditing && (
        <div className="flex gap-2 mb-4 justify-end">
          <Button type="cancel" label="Cancel" onClick={handleCancel} />
          <Button type="primary" label="Save Change" onClick={handleSave} />
        </div>
      )}
    </div>
  );
};

export default EditableSection;

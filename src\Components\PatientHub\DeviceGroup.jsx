import React, { useState, useEffect } from "react";
import GenericTable from "../GenericTable";
import Swal from "sweetalert2";
import { toast, ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { FiEdit2 } from "react-icons/fi";
import { MdDelete } from "react-icons/md";
import AddDeviceGroupModal from "./AddDeviceGroupModal";
import EditDeviceGroupModal from "./EditDeviceGroupModal";
import ViewDeviceGroupModal from "./ViewDeviceGroupModal";

const DeviceGroup = () => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isEditModalOpen, setEditModalOpen] = useState(false);
  const [isViewModalOpen, setViewModalOpen] = useState(false);
  const [selectedRow, setSelectedRow] = useState(null);
  const [viewRow, setViewRow] = useState(null);

  // Load dummy data once
  useEffect(() => {
    const response = [
      { id: 1, deviceGroup: "group 1", assignApp: "NDA, re-check-in", status: "Active" },
      { id: 2, deviceGroup: "group 2", assignApp: "Doctor's Office Visit", status: "Active" },
      { id: 3, deviceGroup: "group 3", assignApp: "Expedite Check-in", status: "Active" },
      { id: 4, deviceGroup: "group 4", assignApp: "Walk-in Guest", status: "Inactive" },
      { id: 5, deviceGroup: "group 5", assignApp: "Guest Verification", status: "Active" },
      { id: 6, deviceGroup: "group 6", assignApp: "Re-Check-in", status: "Active" },
      { id: 7, deviceGroup: "group 7", assignApp: "Re-Check-in", status: "Active" },
    ];
    setData(response);
    setFilteredData(response);
  }, []);

  // Search handler
  const handleSearch = e => setSearchTerm(e.target.value);

  // Filter whenever data or searchTerm changes
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredData(data);
    } else {
      const term = searchTerm.trim().toLowerCase();
      setFilteredData(
        data.filter(row =>
          row.deviceGroup.toLowerCase().includes(term) ||
          row.assignApp.toLowerCase().includes(term) ||
          row.status.toLowerCase().includes(term)
        )
      );
    }
  }, [searchTerm, data]);

  const handleAdd = () => setAddModalOpen(true);

  const handleEdit = row => {
    setSelectedRow(row);
    setEditModalOpen(true);
  };

  const handleView = row => {
    setViewRow(row);
    setViewModalOpen(true);
  };

  const handleDelete = id => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this device group?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(result => {
      if (result.isConfirmed) {
        const updated = data.filter(item => item.id !== id);
        setData(updated);
        setFilteredData(updated);
        toast.success("Device group deleted successfully!");
      }
    });
  };

  // When AddDeviceGroupModal calls onSave
  const handleAddSave = newDeviceGroup => {
    // ensure unique ID in a real app you’d get this from the backend
    const withId = { id: Date.now(), ...newDeviceGroup };
    const updated = [withId, ...data];
    setData(updated);
    setFilteredData(updated);
    setAddModalOpen(false);
    toast.success("Device group added successfully!");
  };

  // When EditDeviceGroupModal calls onSave
  const handleEditSave = updatedRow => {
    const updated = data.map(item =>
      item.id === updatedRow.id ? updatedRow : item
    );
    setData(updated);
    setFilteredData(updated);
    setEditModalOpen(false);
    toast.success("Device group updated successfully!");
  };

  const columns = [
    {
      id: "deviceGroup",
      name: "Device Group",
      selector: row => row.deviceGroup,
      sortable: true,
      cell: row => (
        <span
          className="text-[#4F2683] underline cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.deviceGroup}
        </span>
      ),
    },
    {
      id: "assignApp",
      name: "Assign App",
      selector: row => row.assignApp,
      sortable: true,
    },
    {
      id: "status",
      name: "Status",
      selector: row => row.status,
      sortable: true,
      cell: row => (
        <span
          className={`px-3 py-1 rounded-full text-xs font-semibold ${
            row.status === "Active"
              ? "bg-[#F4F2F7] text-[#4F2683]"
              : "bg-gray-200 text-gray-500"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      button: true,
      ignoreRowClick: true,
      allowOverflow: true,
      cell: row => (
        <div className="flex items-center gap-2">
          <FiEdit2
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#4F2683]"
            title="Edit"
            onClick={() => handleEdit(row)}
          />
          <MdDelete
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer text-[#E74C3C]"
            title="Delete"
            onClick={() => handleDelete(row.id)}
          />
        </div>
      ),
    },
  ];

  return (
    <div className="relative">
      <div className="bg-white rounded-[10px] p-4">
        <GenericTable
          title="Device Group"
          searchTerm={searchTerm}
          onSearchChange={handleSearch}
          onAdd={handleAdd}
          columns={columns}
          data={filteredData}
          showSearch
          showAddButton
        />
      </div>

      <ToastContainer />

      <AddDeviceGroupModal
        isOpen={isAddModalOpen}
        onClose={() => setAddModalOpen(false)}
        onSave={handleAddSave}
      />

      <EditDeviceGroupModal
        isOpen={isEditModalOpen}
        selectedRow={selectedRow}
        onClose={() => setEditModalOpen(false)}
        onSave={handleEditSave}
      />

      <ViewDeviceGroupModal
        isOpen={isViewModalOpen}
        row={viewRow}
        onClose={() => setViewModalOpen(false)}
      />
    </div>
  );
};

export default DeviceGroup;

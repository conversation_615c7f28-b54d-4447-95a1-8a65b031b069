import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { createDocument } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { useCountryMasterData } from "../../../hooks/useCountryMasterData";
import { useStateMasterData } from "../../../hooks/useStateMasterData";
import { useDocumentMasterData } from "../../../hooks/useDocumentMasterData";

// Validation schema - removed document_name as API doesn't accept it
const documentSchema = yup.object().shape({
  document_type: yup.string().required("Document type is required"),
  document_number: yup.string().required("Document number is required"),
  issue_date: yup.date()
    .typeError('Invalid date')
    .required("Issue date is required"),
  expiry_date: yup.date()
    .typeError('Invalid date')
    .required("Expiry date is required"),
  status: yup.string().required("Status is required"),
  country_id: yup.string(), // Made optional since API doesn't require it
  state_id: yup.string(),   // Made optional since API doesn't require it
  other_issuer: yup.string(),
  note: yup.string(),
});

const AddDocumentForm = ({ onClose, onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [filePreview, setFilePreview] = useState(null);
  const [selectedCountry, setSelectedCountry] = useState(null);
  const [show, setShow] = useState(false);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  // Master data hooks
  const { countries } = useCountryMasterData();
  const states = useStateMasterData(selectedCountry);
  const { documentTypeOptions, statusOptions } = useDocumentMasterData();

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(documentSchema),
  });

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Fallback data in case API is not working
  const [fallbackCountries] = useState([
    { country_id: 1, id: 1, name: "United States" },
    { country_id: 2, id: 2, name: "Canada" },
    { country_id: 3, id: 3, name: "India" },
    { country_id: 4, id: 4, name: "Australia" },
    { country_id: 5, id: 5, name: "United Kingdom" },
  ]);

  const [fallbackStates] = useState({
    1: [
      { state_id: 1, id: 1, name: "California" },
      { state_id: 2, id: 2, name: "New York" },
      { state_id: 3, id: 3, name: "Texas" },
      { state_id: 4, id: 4, name: "Florida" },
    ],
    2: [
      { state_id: 5, id: 5, name: "Ontario" },
      { state_id: 6, id: 6, name: "Quebec" },
      { state_id: 7, id: 7, name: "British Columbia" },
    ],
    3: [
      { state_id: 8, id: 8, name: "Maharashtra" },
      { state_id: 9, id: 9, name: "Karnataka" },
      { state_id: 10, id: 10, name: "Tamil Nadu" },
      { state_id: 11, id: 11, name: "Delhi" },
    ],
    4: [
      { state_id: 12, id: 12, name: "New South Wales" },
      { state_id: 13, id: 13, name: "Victoria" },
      { state_id: 14, id: 14, name: "Queensland" },
    ],
    5: [
      { state_id: 15, id: 15, name: "England" },
      { state_id: 16, id: 16, name: "Scotland" },
      { state_id: 17, id: 17, name: "Wales" },
    ],
  });

  // Use API data if available, otherwise use fallback
  const availableCountries = Array.isArray(countries) && countries.length > 0 ? countries : fallbackCountries;
  const availableStates = Array.isArray(states) && states.length > 0 ? states : (fallbackStates[selectedCountry] || []);

  // Debug logging
  console.log("Countries from hook:", countries);
  console.log("States from hook:", states);
  console.log("Selected country:", selectedCountry);
  console.log("Available countries:", availableCountries);
  console.log("Available states:", availableStates);
  console.log("Document type options:", documentTypeOptions);
  console.log("Status options:", statusOptions);

  // Debug dropdown values
  console.log("Document type options values:", documentTypeOptions.map(opt => ({ label: opt.label, value: opt.value, type: typeof opt.value })));
  console.log("Status options values:", statusOptions.map(opt => ({ label: opt.label, value: opt.value, type: typeof opt.value })));

  // Handle file selection
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        toast.error("Please select a valid file (JPEG, PNG, or PDF)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB in bytes
      if (file.size > maxSize) {
        toast.error("File size must be less than 5MB");
        return;
      }

      setSelectedFile(file);

      // Create preview for images
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => setFilePreview(e.target.result);
        reader.readAsDataURL(file);
      } else {
        setFilePreview(null);
      }
    }
  };

  // Remove selected file
  const removeFile = () => {
    setSelectedFile(null);
    setFilePreview(null);
  };

  const onSubmitForm = async (data) => {
    setLoading(true);
    try {
      console.log("Form data before formatting:", data);
      console.log("Document type value:", data.document_type, "Type:", typeof data.document_type);
      console.log("Status value:", data.status, "Type:", typeof data.status);
      console.log("Identity ID:", identityId, "Type:", typeof identityId);
      console.log("Available document type options:", documentTypeOptions);
      console.log("Available status options:", statusOptions);

      let requestData;

      if (selectedFile) {
        // Use FormData for file upload
        const formData = new FormData();

        // Add form fields - remove document_name as API doesn't accept it
        const documentType = parseInt(data.document_type);
        const statusValue = parseInt(data.status);

        console.log("FormData - Before conversion - document_type:", data.document_type, "status:", data.status);
        console.log("FormData - After conversion - document_type:", documentType, "status:", statusValue);

        formData.append('document_type', documentType);
        formData.append('document_number', data.document_number);
        formData.append('issue_date', data.issue_date instanceof Date ? data.issue_date.toISOString().split('T')[0] : data.issue_date);
        formData.append('status', statusValue);
        formData.append('identity_id', identityId);

        // Add optional fields only if they have values
        if (data.expiry_date) {
          formData.append('expiration_date', data.expiry_date instanceof Date ? data.expiry_date.toISOString().split('T')[0] : data.expiry_date);
        }
        if (data.country_id) formData.append('country_id', data.country_id);
        if (data.state_id) formData.append('state_id', data.state_id);
        if (data.other_issuer) formData.append('other_issuer', data.other_issuer);
        if (data.note) formData.append('note', data.note);
        formData.append('document', selectedFile);

        console.log("FormData being sent:");
        for (let pair of formData.entries()) {
          console.log(pair[0] + ': ' + pair[1]);
        }

        requestData = formData;
      } else {
        // Use JSON for data without file - try minimal approach
        const documentType = parseInt(data.document_type);
        const statusValue = parseInt(data.status);

        console.log("Before conversion - document_type:", data.document_type, "status:", data.status);
        console.log("After conversion - document_type:", documentType, "status:", statusValue);

        const jsonData = {
          document_type: documentType,
          document_number: data.document_number,
          issue_date: data.issue_date instanceof Date ? data.issue_date.toISOString().split('T')[0] : data.issue_date,
          status: statusValue,
          identity_id: identityId
        };

        // Add optional fields only if they have values
        if (data.expiry_date) {
          jsonData.expiration_date = data.expiry_date instanceof Date ? data.expiry_date.toISOString().split('T')[0] : data.expiry_date;
        }
        if (data.country_id) jsonData.country_id = data.country_id;
        if (data.state_id) jsonData.state_id = data.state_id;
        if (data.other_issuer) jsonData.other_issuer = data.other_issuer;
        if (data.note) jsonData.note = data.note;

        console.log("JSON data being sent:", jsonData);
        console.log("Document type:", jsonData.document_type, "Type:", typeof jsonData.document_type);
        console.log("Status:", jsonData.status, "Type:", typeof jsonData.status);
        requestData = jsonData;
      }

      const response = await createDocument(requestData);
      console.log("Create document response:", response);

      toast.success("Document added successfully!");
      onSubmit();
    } catch (error) {
      console.error("Error creating document:", error);
      const errorMessage = error.response?.data?.message ||
                          error.response?.data?.error ||
                          error.message ||
                          "Failed to add document. Please try again.";
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`} style={{ willChange: "transform" }}>
      <div className="p-6 bg-white w-full max-w-3xl h-full overflow-y-auto rounded-lg shadow-lg">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Document</h2>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <form onSubmit={handleSubmit(onSubmitForm)} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">


            {/* Document Type */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Type *
              </label>
              <Controller
                name="document_type"
                control={control}
                render={({ field }) => (
                  <CustomDropdown
                    value={field.value}
                    options={documentTypeOptions}
                    placeholder="Select document type"
                    onSelect={field.onChange}
                    className="w-full"
                  />
                )}
              />
              {errors.document_type && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.document_type.message}
                </p>
              )}
            </div>

            {/* Document Number */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Document Number *
              </label>
              <Input
                {...register("document_number")}
                placeholder="Enter document number"
                className="w-full"
              />
              {errors.document_number && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.document_number.message}
                </p>
              )}
            </div>

            {/* Status */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status *
              </label>
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <CustomDropdown
                    value={field.value}
                    options={statusOptions}
                    placeholder="Select status"
                    onSelect={field.onChange}
                    className="w-full"
                  />
                )}
              />
              {errors.status && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.status.message}
                </p>
              )}
            </div>

            {/* Issue Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Issue Date *
              </label>
              <Controller
                name="issue_date"
                control={control}
                render={({ field }) => (
                  <DateInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select issue date"
                  />
                )}
              />
              {errors.issue_date && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.issue_date.message}
                </p>
              )}
            </div>

            {/* Expiry Date */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Expiry Date *
              </label>
              <Controller
                name="expiry_date"
                control={control}
                render={({ field }) => (
                  <DateInput
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select expiry date"
                  />
                )}
              />
              {errors.expiry_date && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.expiry_date.message}
                </p>
              )}
            </div>

            {/* Country */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Country *
              </label>
              <Controller
                name="country_id"
                control={control}
                render={({ field }) => (
                  <CustomDropdown
                    value={field.value}
                    options={availableCountries.map(country => ({
                      value: (country.country_id || country.id)?.toString() || '',
                      label: country.name || ''
                    }))}
                    placeholder="Select country"
                    onSelect={(value) => {
                      field.onChange(value);
                      setSelectedCountry(value);
                    }}
                    className="w-full"
                  />
                )}
              />
              {errors.country_id && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.country_id.message}
                </p>
              )}
            </div>

            {/* State */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                State *
              </label>
              <Controller
                name="state_id"
                control={control}
                render={({ field }) => (
                  <CustomDropdown
                    value={field.value}
                    options={availableStates.map(state => ({
                      value: (state.state_id || state.id)?.toString() || '',
                      label: state.name || ''
                    }))}
                    placeholder="Select state"
                    onSelect={field.onChange}
                    className="w-full"
                    disabled={!selectedCountry}
                  />
                )}
              />
              {errors.state_id && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.state_id.message}
                </p>
              )}
            </div>

            {/* Other Issuer */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Other Issuer
              </label>
              <Input
                {...register("other_issuer")}
                placeholder="Enter other issuer"
                className="w-full"
              />
              {errors.other_issuer && (
                <p className="text-red-500 text-sm mt-1">
                  {errors.other_issuer.message}
                </p>
              )}
            </div>
          </div>

          {/* Note Field - Full Width */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Note
            </label>
            <textarea
              {...register("note")}
              placeholder="Enter any additional notes"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#4F2683] focus:border-transparent"
              rows={3}
            />
            {errors.note && (
              <p className="text-red-500 text-sm mt-1">
                {errors.note.message}
              </p>
            )}
          </div>

          {/* File Upload Section */}
          <div className="mt-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Document File (Optional)
            </label>
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6">
              {!selectedFile ? (
                <div className="text-center">
                  <input
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    onChange={handleFileChange}
                    className="hidden"
                    id="document-file"
                  />
                  <label
                    htmlFor="document-file"
                    className="cursor-pointer inline-flex items-center px-4 py-2 bg-[#4F2683] text-white rounded-lg hover:bg-[#3d1f66]"
                  >
                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Choose File
                  </label>
                  <p className="text-sm text-gray-500 mt-2">
                    Supported formats: JPEG, PNG, PDF (Max 5MB)
                  </p>
                </div>
              ) : (
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    {filePreview ? (
                      <img
                        src={filePreview}
                        alt="Preview"
                        className="w-16 h-16 object-cover rounded-lg mr-4"
                      />
                    ) : (
                      <div className="w-16 h-16 bg-gray-200 rounded-lg mr-4 flex items-center justify-center">
                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                      <p className="text-sm text-gray-500">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={removeFile}
                    className="text-red-500 hover:text-red-700"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-[#4F2683] text-white rounded-lg hover:bg-[#3d1f66] disabled:opacity-50"
              disabled={loading}
            >
              {loading ? "Adding..." : "Add Document"}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDocumentForm;
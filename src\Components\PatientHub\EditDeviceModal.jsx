import React, { useState, useEffect } from "react";

const EditDeviceModal = ({ open, onClose, onSave, selectedRow, setSelectedRow }) => {
  const [show, setShow] = useState(false);

  useEffect(() => {
    if (open) {
      setTimeout(() => setShow(true), 10);
    } else {
      setShow(false);
    }
  }, [open]);

  if (!open || !selectedRow) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Edit Device
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form
          onSubmit={e => {
            e.preventDefault();
            onSave();
          }}
          className="bg-white p-6 rounded-lg"
        >
          <div className="flex items-center mb-4">
            <label htmlFor="device" className="w-1/4 text-[16px] font-normal">
              Device
            </label>
            <div className="w-3/4">
              <input
                id="device"
                name="device"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.device}
                onChange={e => setSelectedRow({ ...selectedRow, device: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="deviceGroup" className="w-1/4 text-[16px] font-normal">
              Device Group
            </label>
            <div className="w-3/4">
              <input
                id="deviceGroup"
                name="deviceGroup"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.deviceGroup}
                onChange={e => setSelectedRow({ ...selectedRow, deviceGroup: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="building" className="w-1/4 text-[16px] font-normal">
              Building
            </label>
            <div className="w-3/4">
              <input
                id="building"
                name="building"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.building}
                onChange={e => setSelectedRow({ ...selectedRow, building: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="floor" className="w-1/4 text-[16px] font-normal">
              Floor
            </label>
            <div className="w-3/4">
              <input
                id="floor"
                name="floor"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.floor}
                onChange={e => setSelectedRow({ ...selectedRow, floor: e.target.value })}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="room" className="w-1/4 text-[16px] font-normal">
              Room
            </label>
            <div className="w-3/4">
              <input
                id="room"
                name="room"
                type="text"
                className="w-full border bg-transparent rounded p-2 focus:outline-none"
                value={selectedRow.room}
                onChange={e => setSelectedRow({ ...selectedRow, room: e.target.value })}
              />
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            <button
              type="button"
              onClick={handleClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default EditDeviceModal;

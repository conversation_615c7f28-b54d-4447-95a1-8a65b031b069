import React, { useState } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import Input from "../../Global/Input/Input";
import Button from "../../Global/Button";
import DateInput from "../../Global/Input/DateInput";
import CustomDropdown from "../../Global/CustomDropdown";
import { createDelegate } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom";

const dummyStatusOptions = [
  { label: "Active", value: 1 },
  { label: "Inactive", value: 2 },
];

const delegateSchema = yup.object().shape({
  name: yup.string().required("Name is required"),
  taskTypes: yup.string().required("Task is required"),
  start_date: yup.date().required("Start Date is required"),
  end_date: yup.date().required("End Date is required"),
  status: yup.number().required("Status is required"),
});

const AddDelegateForm = ({ onClose, onSubmit }) => {
  const [loading, setLoading] = useState(false);
  const [show, setShow] = useState(false);

  // Get identity_id from URL parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id");

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(delegateSchema),
  });

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const submitFormHandler = async (data) => {
    setLoading(true);
    try {
      const delegateData = {
        name: data.name,
        task_to_delegate: data.taskTypes,
        start_date: data.start_date,
        end_date: data.end_date,
        status: data.status === "Active" ? "0" : "1",
        identity_id: identityId,
      };

      const result = await createDelegate(delegateData);
      toast.success("Delegate added successfully!");
      onSubmit(result);
      onClose();
    } catch (error) {
      console.error("Error adding delegate:", error);
      toast.error("Failed to add delegate. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50 transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`} style={{ willChange: "transform" }}>
      <div className="bg-white w-full h-full max-w-3xl p-4 rounded-lg shadow-lg overflow-y-auto">
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">Add Delegate</h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit(submitFormHandler)} className="bg-white p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">Delegate Details</h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">Delegate</label>
            <div className="w-3/4">
              <Input {...register("name")} placeholder="Enter Name" />
              {errors.name && <p className="text-red-500 text-sm">{errors.name.message}</p>}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">Task To Delegate</label>
            <div className="w-3/4">
              <Input {...register("taskTypes")} placeholder="Tasks" />
              {errors.taskTypes && <p className="text-red-500 text-sm">{errors.taskTypes.message}</p>}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">Start Date</label>
            <div className="w-3/4">
              <Controller
                control={control}
                name="start_date"
                render={({ field }) => (
                  <DateInput
                    {...field}
                    placeholder="MM-DD-YYYY"
                    onChange={(date) => field.onChange(date)}
                  />
                )}
              />
              {errors.start_date && <p className="text-red-500 text-sm">{errors.start_date.message}</p>}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">End Date</label>
            <div className="w-3/4">
              <Controller
                control={control}
                name="end_date"
                render={({ field }) => (
                  <DateInput
                    {...field}
                    placeholder="MM-DD-YYYY"
                    onChange={(date) => field.onChange(date)}
                  />
                )}
              />
              {errors.end_date && <p className="text-red-500 text-sm">{errors.end_date.message}</p>}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">Status</label>
            <div className="w-3/4">
              <Controller
                name="status"
                control={control}
                render={({ field }) => (
                  <CustomDropdown
                  className="h-10"
                    options={dummyStatusOptions}
                    value={field.value}
                    onSelect={field.onChange}
                    placeholder="Select Status"
                    error={errors.status}
                  />
                )}
              />
              {errors.status && <p className="text-red-500 text-sm">{errors.status.message}</p>}
            </div>
          </div>
          <div className="flex gap-4 pb-4 justify-center">
            <Button type="button" onClick={onClose} label="Cancel" className="bg-gray-400 text-white" />
            <Button type="submit" label="Add" loading={loading} className="bg-[#4F2683] text-white" />
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDelegateForm;
